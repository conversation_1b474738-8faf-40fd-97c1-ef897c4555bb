"""
Excel风控数据处理器
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Union, Tuple
import logging
from datetime import datetime
import hashlib

from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter

from .risk_config import RiskAnalysisConfig

# 设置日志
logger = logging.getLogger(__name__)


class ExcelRiskProcessor:
    """Excel风控数据处理器"""

    def __init__(
        self, encoding: str = "utf-8", chunk_size: int = 1000, chunk_overlap: int = 200
    ):
        """
        初始化Excel处理器

        Args:
            encoding: 文件编码
            chunk_size: 文档分块大小
            chunk_overlap: 分块重叠大小
        """
        self.encoding = encoding
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.risk_config = RiskAnalysisConfig()

        # 初始化文档分割器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", " ", ""],
        )

        logger.info(f"Excel风控数据处理器初始化完成，分块大小: {chunk_size}")

    def validate_excel_file(self, file_path: Union[str, Path]) -> bool:
        """
        验证Excel文件是否有效

        Args:
            file_path: Excel文件路径

        Returns:
            是否有效
        """
        file_path = Path(file_path)

        # 检查文件是否存在
        if not file_path.exists():
            logger.error(f"Excel文件不存在: {file_path}")
            return False

        # 检查文件扩展名
        if file_path.suffix.lower() not in [".xlsx", ".xls"]:
            logger.error(f"不支持的文件格式: {file_path.suffix}")
            return False

        # 尝试读取文件
        try:
            pd.read_excel(file_path, nrows=1)
            return True
        except Exception as e:
            logger.error(f"Excel文件读取失败: {e}")
            return False

    def generate_file_code_from_content(self, file_path: Union[str, Path]) -> str:
        """
        基于文件内容生成唯一编码

        Args:
            file_path: 文件路径

        Returns:
            文件编码
        """
        file_path = Path(file_path)

        try:
            # 读取文件内容并计算哈希
            with open(file_path, "rb") as f:
                content = f.read()
                content_hash = hashlib.md5(content).hexdigest()[:12]

            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 组合文件编码
            file_code = f"{file_path.stem}_{timestamp}_{content_hash}"

            logger.info(f"生成文件编码: {file_code}")
            return file_code

        except Exception as e:
            logger.error(f"生成文件编码失败: {e}")
            # 使用备用方案
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"{file_path.stem}_{timestamp}_backup"

    def read_excel_sheets(self, file_path: Union[str, Path]) -> Dict[str, pd.DataFrame]:
        """
        读取Excel的所有工作表

        Args:
            file_path: Excel文件路径

        Returns:
            工作表名称到DataFrame的映射
        """
        try:
            # 读取所有工作表
            excel_data = pd.read_excel(file_path, sheet_name=None)

            logger.info(f"成功读取Excel文件，包含 {len(excel_data)} 个工作表")
            for sheet_name in excel_data.keys():
                logger.info(
                    f"  - 工作表: {sheet_name}, 行数: {len(excel_data[sheet_name])}"
                )

            return excel_data

        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            return {}

    def extract_excel_content_as_text(self, df: pd.DataFrame) -> str:
        """
        将DataFrame内容提取为文本，用作知识库

        Args:
            df: 数据框

        Returns:
            格式化的文本内容
        """
        try:
            # 将整个DataFrame转换为易读的文本格式
            content_parts = []

            # 添加基本信息
            content_parts.append(f"数据行数: {len(df)}")
            content_parts.append(f"数据列数: {len(df.columns)}")
            content_parts.append("")

            # 添加列名信息
            content_parts.append("数据字段:")
            for i, col in enumerate(df.columns, 1):
                content_parts.append(f"{i}. {col}")
            content_parts.append("")

            # 添加详细数据内容
            content_parts.append("详细数据内容:")
            content_parts.append(df.to_string(index=True, max_rows=None))
            content_parts.append("")

            # 添加数据统计信息
            content_parts.append("数据统计信息:")
            try:
                numeric_df = df.select_dtypes(include=[np.number])
                if not numeric_df.empty:
                    content_parts.append(numeric_df.describe().to_string())
            except Exception:
                pass

            result = "\n".join(content_parts)
            logger.info(f"成功提取Excel内容，文本长度: {len(result)}")
            return result

        except Exception as e:
            logger.error(f"提取Excel内容失败: {e}")
            return f"Excel数据提取失败: {str(e)}"

    # 删除不再需要的字段查找方法，因为我们直接将Excel内容作为知识库

    # 注意：不再需要计算衍生指标，直接将原始数据交给AI分析

    def create_documents_from_excel(
        self, file_path: Union[str, Path], file_code: str
    ) -> Tuple[List[Document], Dict[str, Any]]:
        """
        从Excel文件创建文档对象

        Args:
            file_path: Excel文件路径
            file_code: 文件编码

        Returns:
            文档列表和文件信息的元组
        """
        file_path = Path(file_path)
        documents = []

        try:
            # 读取Excel数据
            excel_data = self.read_excel_sheets(file_path)

            if not excel_data:
                return [], {}

            # 处理所有工作表
            sheet_summaries = []

            for sheet_name, df in excel_data.items():
                # 将工作表内容提取为文本
                sheet_content = self.extract_excel_content_as_text(df)

                # 创建工作表摘要
                sheet_summary = f"""
=== 工作表: {sheet_name} ===

{sheet_content}

=== 工作表 {sheet_name} 结束 ===
"""

                sheet_summaries.append(sheet_summary)

                # 为每个工作表创建一个文档，然后进行分割
                doc = Document(
                    page_content=sheet_summary,
                    metadata={
                        "source": str(file_path),
                        "file_name": file_path.name,
                        "file_code": file_code,
                        "sheet_name": sheet_name,
                        "data_type": "risk_data",
                        "created_at": datetime.now().isoformat(),
                    },
                )

                # 使用文档分割器分割大文档
                try:
                    chunks = self.text_splitter.split_documents([doc])
                    logger.info(f"工作表 {sheet_name} 分割为 {len(chunks)} 个文档块")

                    # 为每个分块添加额外的元数据
                    for i, chunk in enumerate(chunks):
                        chunk.metadata.update(
                            {
                                "chunk_id": i,
                                "chunk_size": len(chunk.page_content),
                                "total_chunks": len(chunks),
                                "original_sheet": sheet_name,
                            }
                        )

                    documents.extend(chunks)
                except Exception as e:
                    logger.warning(f"工作表 {sheet_name} 分割失败，使用原始文档: {e}")
                    documents.append(doc)

            # 创建综合风控数据报告文档
            comprehensive_report = f"""
=== 综合风控数据报告 ===
文件: {file_path.name}
文件编码: {file_code}
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

工作表数量: {len(excel_data)}

=== 各工作表详细数据 ===
"""

            comprehensive_report += "\n".join(sheet_summaries)

            # 创建综合报告文档并进行分割
            comprehensive_doc = Document(
                page_content=comprehensive_report,
                metadata={
                    "source": str(file_path),
                    "file_name": file_path.name,
                    "file_code": file_code,
                    "data_type": "comprehensive_risk_report",
                    "sheet_count": len(excel_data),
                    "created_at": datetime.now().isoformat(),
                },
            )

            # 分割综合报告文档
            try:
                report_chunks = self.text_splitter.split_documents([comprehensive_doc])
                logger.info(f"综合报告分割为 {len(report_chunks)} 个文档块")

                # 为每个分块添加额外的元数据
                for i, chunk in enumerate(report_chunks):
                    chunk.metadata.update(
                        {
                            "chunk_id": i,
                            "chunk_size": len(chunk.page_content),
                            "total_chunks": len(report_chunks),
                            "is_comprehensive_report": True,
                        }
                    )

                documents.extend(report_chunks)
            except Exception as e:
                logger.warning(f"综合报告分割失败，使用原始文档: {e}")
                documents.append(comprehensive_doc)

            # 文件信息
            file_info = {
                "file_path": str(file_path),
                "file_name": file_path.name,
                "file_size": file_path.stat().st_size,
                "sheet_count": len(excel_data),
                "processed_at": datetime.now().isoformat(),
            }

            logger.info(f"成功处理Excel文件，创建 {len(documents)} 个文档")
            return documents, file_info

        except Exception as e:
            logger.error(f"处理Excel文件失败: {e}")
            return [], {}

    def process_excel_file(
        self, file_path: Union[str, Path]
    ) -> Tuple[str, List[Document], Dict[str, Any]]:
        """
        完整处理Excel文件

        Args:
            file_path: Excel文件路径

        Returns:
            文件编码、文档列表和文件信息的元组
        """
        file_path = Path(file_path)

        # 验证文件
        if not self.validate_excel_file(file_path):
            return "", [], {}

        # 生成文件编码
        file_code = self.generate_file_code_from_content(file_path)

        # 创建文档
        documents, file_info = self.create_documents_from_excel(file_path, file_code)

        logger.info(f"Excel文件处理完成: {file_path.name} -> {file_code}")
        return file_code, documents, file_info
