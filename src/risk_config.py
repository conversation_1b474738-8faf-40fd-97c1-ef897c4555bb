"""
风控分析专用配置模块
"""

from typing import Dict, Any
from enum import Enum


class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "低风险"
    MEDIUM = "中等风险"
    HIGH = "高风险"
    CRITICAL = "极高风险"


# 删除不再需要的指标枚举，因为AI会自动分析Excel中的任意字段


# 删除不再需要的风险阈值类，因为AI会自动评估风险


class RiskAnalysisConfig:
    """风控分析配置类"""
    
    # 个人信贷风险评估维度权重
    RISK_WEIGHTS = {
        "收入能力": 0.30,
        "负债状况": 0.25,
        "信用历史": 0.20,
        "个人稳定性": 0.15,
        "资产状况": 0.10
    }

    # 删除不再需要的风险阈值配置，因为AI会自动评估风险
    
    # 个人信贷风控分析prompt模板
    RISK_ANALYSIS_PROMPT = """你是一位专业的个人信贷风控分析师，请基于提供的个人信贷数据进行全面的违约风险评估分析。

请按照以下结构输出完整的个人信贷风险分析报告：

## 1. 违约概率评估
基于个人信贷数据计算违约概率，并说明计算依据。

## 2. 风险等级判定
- 综合风险等级：[低风险/中等风险/高风险/极高风险]
- 风险等级依据：详细说明风险等级判定的关键因素

## 3. 关键信贷指标分析
分析以下关键指标：
- 月收入：[数值] - [分析收入水平和稳定性]
- 负债收入比：[数值] - [分析负债压力]
- 信用评分：[数值] - [分析信用历史]
- 工作年限：[数值] - [分析职业稳定性]
- 年龄：[数值] - [分析年龄风险]
- 教育水平：[分析] - [教育背景对风险的影响]
- 贷款金额：[数值] - [分析贷款规模合理性]
- 现有负债：[数值] - [分析债务负担]
- 资产价值：[数值] - [分析资产保障]

## 4. 个人信贷风险因子识别
识别主要风险因子：
- 收入风险：[收入水平、稳定性分析]
- 负债风险：[负债水平、结构分析]
- 信用风险：[信用历史、还款记录分析]
- 稳定性风险：[工作、居住稳定性分析]
- 其他风险：[年龄、教育等其他因素分析]

## 5. 风险预警信号
列出需要关注的个人信贷风险预警信号。

## 6. 信贷建议
提供具体的信贷决策建议：
- 是否批准贷款
- 建议贷款额度
- 建议利率水平
- 风险缓释措施

请基于以下个人信贷数据进行分析：
{personal_credit_data}

要求：
1. 分析要客观、专业、准确
2. 数据引用要具体、准确
3. 结论要有充分依据
4. 建议要具有可操作性
5. 重点关注个人违约风险
"""

    # 风险报告输出格式
    RISK_REPORT_FORMAT = {
        "file_code": "",
        "analysis_timestamp": "",
        "default_probability": 0.0,
        "risk_level": "",
        "risk_score": 0.0,
        "key_indicators": {},
        "risk_factors": [],
        "warning_signals": [],
        "recommendations": [],
        "analysis_summary": "",
        "data_source": ""
    }
    
    # 注意：不需要预设字段映射，直接将Excel内容作为知识库
    # Excel文件包含各种风控特征指标，让AI自动分析
    
    @classmethod
    def get_risk_analysis_prompt(cls, personal_credit_data: str) -> str:
        """获取个人信贷风控分析prompt"""
        return cls.RISK_ANALYSIS_PROMPT.format(personal_credit_data=personal_credit_data)
    
    @classmethod
    def get_default_risk_report(cls) -> Dict[str, Any]:
        """获取默认风险报告格式"""
        return cls.RISK_REPORT_FORMAT.copy()
    
    # 删除不再需要的风险评分计算方法，因为AI会直接给出风险等级
