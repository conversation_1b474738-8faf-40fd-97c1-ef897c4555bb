"""
风险预测分析器
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
import json

from langchain_core.documents import Document
from langchain_deepseek import ChatDeepSeek

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from config import settings
    from src.file_coded_vector_store import FileCodedVectorStore
    from src.risk_config import RiskAnalysisConfig, RiskLevel
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.error(f"导入配置失败: {e}")

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RiskAnalyzer:
    """风险预测分析器"""

    def __init__(
        self,
        file_coded_vector_store: Optional[FileCodedVectorStore] = None,
        api_key: Optional[str] = None,
        model_name: str = "deepseek-reasoner",
        temperature: float = 0.1,
        max_tokens: int = None,
    ):
        """
        初始化风险分析器

        Args:
            file_coded_vector_store: 文件编码向量存储管理器
            api_key: DeepSeek API 密钥
            model_name: 模型名称
            temperature: 生成温度
            max_tokens: 最大生成长度
        """
        # 初始化向量存储管理器
        if file_coded_vector_store is None:
            self.vector_store_manager = FileCodedVectorStore()
        else:
            self.vector_store_manager = file_coded_vector_store

        # 配置 DeepSeek
        self.api_key = (
            api_key or settings.DEEPSEEK_API_KEY or os.getenv("DEEPSEEK_API_KEY")
        )
        self.model_name = model_name or settings.DEEPSEEK_MODEL
        self.temperature = temperature
        self.max_tokens = max_tokens or settings.MAX_TOKENS

        if not self.api_key:
            raise ValueError(
                "DeepSeek API 密钥未设置。请设置环境变量 DEEPSEEK_API_KEY 或在配置中提供。"
            )

        # 初始化 LLM
        self.llm = ChatDeepSeek(
            api_key=self.api_key,
            model=self.model_name,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
        )

        # 风控配置
        self.risk_config = RiskAnalysisConfig()

        logger.info("✅ 风险分析器初始化完成")
        logger.info(f"模型: {self.model_name}")
        logger.info(f"温度: {self.temperature}")

    def extract_risk_data_from_documents(self, documents: List[Document]) -> str:
        """
        从文档中提取风控数据

        Args:
            documents: 文档列表

        Returns:
            格式化的风控数据字符串
        """
        if not documents:
            return "未找到相关风控数据"

        risk_data_parts = []
        max_total_length = 50000  # 限制总长度，约12K tokens，留足够空间给prompt和response

        current_length = 0
        for i, doc in enumerate(documents, 1):
            # 获取文档元数据
            file_name = doc.metadata.get("file_name", "未知文件")
            chunk_id = doc.metadata.get("chunk_id", "")
            sheet_name = doc.metadata.get("sheet_name", doc.metadata.get("original_sheet", ""))

            # 现在文档已经在源头被正确分割，不需要再次截断
            content = doc.page_content.strip()

            # 格式化文档内容，包含更多元数据信息
            chunk_info = f" (块 {chunk_id})" if chunk_id != "" else ""
            sheet_info = f" - {sheet_name}" if sheet_name else ""

            data_part = f"""
                数据源 {i}{chunk_info}{sheet_info}:
                文件: {file_name}
                内容: {content}
            ---"""

            # 检查是否会超出总长度限制
            if current_length + len(data_part) > max_total_length:
                logger.warning(f"风控数据总长度超限，已处理 {i-1} 个文档")
                break

            risk_data_parts.append(data_part)
            current_length += len(data_part)

        result = "\n".join(risk_data_parts)
        logger.info(f"提取风控数据完成，总长度: {len(result)} 字符，文档数: {len(risk_data_parts)}")
        return result

    def extract_default_probability_from_analysis(self, analysis_text: str) -> float:
        """
        从AI分析结果中提取违约概率

        Args:
            analysis_text: AI分析文本

        Returns:
            违约概率 (0-1之间)
        """
        # 尝试从分析文本中提取违约概率
        import re

        # 查找百分比格式的违约概率
        percentage_match = re.search(r"违约概率[：:]\s*([0-9.]+)%", analysis_text)
        if percentage_match:
            try:
                return float(percentage_match.group(1)) / 100
            except ValueError:
                pass

        # 查找小数格式的违约概率
        decimal_match = re.search(r"违约概率[：:]\s*([0-9.]+)", analysis_text)
        if decimal_match:
            try:
                prob = float(decimal_match.group(1))
                if prob <= 1.0:
                    return prob
                else:
                    return prob / 100  # 假设是百分比形式
            except ValueError:
                pass

        # 根据风险等级估算违约概率
        if "极高风险" in analysis_text:
            return 0.8
        elif "高风险" in analysis_text:
            return 0.6
        elif "中等风险" in analysis_text:
            return 0.3
        elif "低风险" in analysis_text:
            return 0.1
        else:
            return 0.5  # 默认中等风险

    def extract_risk_level_from_analysis(self, analysis_text: str) -> str:
        """
        从分析文本中提取风险等级

        Args:
            analysis_text: AI分析文本

        Returns:
            风险等级字符串
        """
        # 按优先级查找风险等级
        if "极高风险" in analysis_text:
            return "极高风险"
        elif "高风险" in analysis_text:
            return "高风险"
        elif "中等风险" in analysis_text or "中风险" in analysis_text:
            return "中等风险"
        elif "低风险" in analysis_text:
            return "低风险"
        else:
            return "中等风险"  # 默认

    def generate_risk_report(self, file_code: str) -> Dict[str, Any]:
        """
        生成风险分析报告

        Args:
            file_code: 文件编码

        Returns:
            完整的风险分析报告
        """
        try:
            logger.info(f"开始为文件编码 {file_code} 生成风险报告")

            # 获取文件对应的所有文档
            documents = self.vector_store_manager.get_all_documents_by_code(file_code)

            if not documents:
                return {
                    "success": False,
                    "error": f"未找到文件编码 {file_code} 对应的数据",
                    "file_code": file_code,
                }

            # 提取风控数据
            risk_data = self.extract_risk_data_from_documents(documents)

            # 生成风控分析prompt
            analysis_prompt = self.risk_config.get_risk_analysis_prompt(risk_data)

            # 调用AI进行分析
            logger.info("调用AI进行风险分析...")
            analysis_result = self.llm.invoke(analysis_prompt)

            # 从AI分析结果中提取信息
            default_probability = self.extract_default_probability_from_analysis(
                analysis_result.content
            )
            risk_level = self.extract_risk_level_from_analysis(analysis_result.content)

            # 简化的风险评分（基于违约概率）
            risk_score = default_probability

            # 获取文件信息
            file_info = self.vector_store_manager.get_file_info_by_code(file_code)

            # 构建完整报告
            risk_report = {
                "success": True,
                "file_code": file_code,
                "analysis_timestamp": datetime.now().isoformat(),
                "default_probability": round(default_probability, 4),
                "risk_level": risk_level,
                "risk_score": round(risk_score, 4),
                "analysis_summary": analysis_result.content,
                "document_count": len(documents),
                "data_source": file_info.get("file_info", {}) if file_info else {},
                "model_info": {
                    "model_name": self.model_name,
                    "temperature": self.temperature,
                },
            }

            logger.info(
                f"✅ 风险报告生成完成，违约概率: {default_probability:.2%}, 风险等级: {risk_level}"
            )
            return risk_report

        except Exception as e:
            logger.error(f"生成风险报告失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_code": file_code,
                "analysis_timestamp": datetime.now().isoformat(),
            }

    def get_risk_summary(self, file_code: str) -> str:
        """
        获取风险分析摘要

        Args:
            file_code: 文件编码

        Returns:
            风险分析摘要文本
        """
        report = self.generate_risk_report(file_code)

        if not report.get("success", False):
            return f"风险分析失败: {report.get('error', '未知错误')}"

        summary = f"""
=== 风险分析摘要 ===
文件编码: {report['file_code']}
分析时间: {report['analysis_timestamp']}
违约概率: {report['default_probability']:.2%}
风险等级: {report['risk_level']}
风险评分: {report['risk_score']:.2f}

AI分析结果:
{report['analysis_summary'][:500]}...
"""

        return summary

    def list_available_file_codes(self) -> List[str]:
        """获取所有可用的文件编码"""
        return self.vector_store_manager.list_file_codes()

    def get_analyzer_info(self) -> Dict[str, Any]:
        """获取分析器信息"""
        return {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "vector_store_stats": self.vector_store_manager.get_statistics(),
        }
